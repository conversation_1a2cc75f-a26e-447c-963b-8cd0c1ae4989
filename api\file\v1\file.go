package v1

import (
	"github.com/gogf/gf/v2/frame/g"
)

// 文件上传预签名URL请求
type FileUploadReq struct {
	g.Meta      `path:"/upload-url" tags:"File" method:"get" summary:"获取文件上传预签名URL"`
	ObjectId    int64  `json:"objectId" v:"required" dc:"对象ID（用户ID、食物ID等）"`
	FileType    string `json:"fileType" v:"required|in:avatar,foodimage,document" dc:"文件类型"`
	ContentType string `json:"contentType" v:"required" dc:"文件内容类型"`
	Expiration  int    `json:"expiration" v:"between:1,1440" dc:"URL有效期（分钟）" default:"30"`
}

type FileUploadRes struct {
	UploadUrl string `json:"uploadUrl" dc:"上传预签名URL"`
	FileName  string `json:"fileName" dc:"生成的文件名"`
	FileUrl   string `json:"fileUrl" dc:"文件访问URL"`
}

// 文件下载预签名URL请求
type FileDownloadReq struct {
	g.Meta     `path:"/download-url" tags:"File" method:"get" summary:"获取文件下载预签名URL"`
	FileName   string `json:"fileName" v:"required" dc:"文件名"`
	Expiration int    `json:"expiration" v:"between:1,1440" dc:"URL有效期（分钟）" default:"60"`
}

type FileDownloadRes struct {
	DownloadUrl string `json:"downloadUrl" dc:"下载预签名URL"`
	FileName    string `json:"fileName" dc:"文件名"`
	FileSize    int64  `json:"fileSize" dc:"文件大小（字节）"`
}

// 文件删除请求
type FileDeleteReq struct {
	g.Meta   `path:"/delete" tags:"File" method:"delete" summary:"删除文件"`
	FileName string `json:"fileName" v:"required" dc:"文件名"`
}

type FileDeleteRes struct {
	Success bool   `json:"success" dc:"是否成功"`
	Message string `json:"message" dc:"结果消息"`
}

// 文件列表查询请求
type FileListReq struct {
	g.Meta   `path:"/list" tags:"File" method:"get" summary:"获取文件列表"`
	Page     int    `json:"page" v:"min:1" dc:"页码" default:"1"`
	Size     int    `json:"size" v:"between:1,100" dc:"每页数量" default:"10"`
	FileType string `json:"fileType" v:"in:avatar,foodimage,document" dc:"文件类型筛选"`
	ObjectId *int64 `json:"objectId" dc:"对象ID筛选"`
}

type FileListRes struct {
	List  []FileInfo `json:"list" dc:"文件列表"`
	Total int64      `json:"total" dc:"总数"`
	Page  int        `json:"page" dc:"当前页"`
	Size  int        `json:"size" dc:"每页数量"`
}

type FileInfo struct {
	FileName    string `json:"fileName" dc:"文件名"`
	FileType    string `json:"fileType" dc:"文件类型"`
	ContentType string `json:"contentType" dc:"文件内容类型"`
	FileSize    int64  `json:"fileSize" dc:"文件大小（字节）"`
	ObjectId    int64  `json:"objectId" dc:"关联对象ID"`
	FileUrl     string `json:"fileUrl" dc:"文件访问URL"`
	UploadTime  string `json:"uploadTime" dc:"上传时间"`
}

// 头像上传请求
type AvatarUploadReq struct {
	g.Meta      `path:"/avatar/upload-url" tags:"File" method:"get" summary:"获取头像上传URL"`
	ContentType string `json:"contentType" v:"required" dc:"文件内容类型"`
}

type AvatarUploadRes struct {
	UploadUrl string `json:"uploadUrl" dc:"上传预签名URL"`
	FileName  string `json:"fileName" dc:"生成的文件名"`
	AvatarUrl string `json:"avatarUrl" dc:"头像访问URL"`
}

// 头像下载请求
type AvatarDownloadReq struct {
	g.Meta `path:"/avatar/download-url" tags:"File" method:"get" summary:"获取头像下载URL"`
	UserId *int64 `json:"userId" dc:"用户ID（管理员查询用）"`
}

type AvatarDownloadRes struct {
	AvatarUrl string `json:"avatarUrl" dc:"头像访问URL"`
	FileName  string `json:"fileName" dc:"文件名"`
}

// 食物图片上传请求
type FoodImageUploadReq struct {
	g.Meta      `path:"/food/upload-image-url" tags:"File" method:"get" summary:"获取食物图片上传URL"`
	FoodId      int64  `json:"foodId" v:"required" dc:"食物ID"`
	ContentType string `json:"contentType" v:"required" dc:"文件内容类型"`
}

type FoodImageUploadRes struct {
	UploadUrl string `json:"uploadUrl" dc:"上传预签名URL"`
	FileName  string `json:"fileName" dc:"生成的文件名"`
	ImageUrl  string `json:"imageUrl" dc:"图片访问URL"`
}

// 图片处理请求
type ImageProcessReq struct {
	g.Meta   `path:"/image/process" tags:"File" method:"post" summary:"图片处理"`
	FileName string `json:"fileName" v:"required" dc:"原始文件名"`
	Width    int    `json:"width" v:"between:1,2000" dc:"目标宽度"`
	Height   int    `json:"height" v:"between:1,2000" dc:"目标高度"`
	Quality  int    `json:"quality" v:"between:1,100" dc:"图片质量" default:"80"`
	Format   string `json:"format" v:"in:jpeg,png,webp" dc:"输出格式" default:"jpeg"`
}

type ImageProcessRes struct {
	ProcessedUrl string `json:"processedUrl" dc:"处理后的图片URL"`
	FileName     string `json:"fileName" dc:"处理后的文件名"`
	FileSize     int64  `json:"fileSize" dc:"处理后的文件大小"`
}

// 文件信息查询请求
type FileInfoReq struct {
	g.Meta   `path:"/info" tags:"File" method:"get" summary:"获取文件信息"`
	FileName string `json:"fileName" v:"required" dc:"文件名"`
}

type FileInfoRes struct {
	FileName    string `json:"fileName" dc:"文件名"`
	FileType    string `json:"fileType" dc:"文件类型"`
	ContentType string `json:"contentType" dc:"文件内容类型"`
	FileSize    int64  `json:"fileSize" dc:"文件大小（字节）"`
	ObjectId    int64  `json:"objectId" dc:"关联对象ID"`
	FileUrl     string `json:"fileUrl" dc:"文件访问URL"`
	UploadTime  string `json:"uploadTime" dc:"上传时间"`
	IsPublic    bool   `json:"isPublic" dc:"是否公开访问"`
}

// 批量文件上传请求
type BatchUploadReq struct {
	g.Meta      `path:"/batch-upload-url" tags:"File" method:"post" summary:"批量获取文件上传URL"`
	Files       []BatchUploadFile `json:"files" v:"required" dc:"文件列表"`
	ObjectId    int64             `json:"objectId" v:"required" dc:"对象ID"`
	FileType    string            `json:"fileType" v:"required|in:avatar,foodimage,document" dc:"文件类型"`
	Expiration  int               `json:"expiration" v:"between:1,1440" dc:"URL有效期（分钟）" default:"30"`
}

type BatchUploadFile struct {
	FileName    string `json:"fileName" v:"required" dc:"文件名"`
	ContentType string `json:"contentType" v:"required" dc:"文件内容类型"`
	FileSize    int64  `json:"fileSize" v:"min:1" dc:"文件大小（字节）"`
}

type BatchUploadRes struct {
	Files []BatchUploadResult `json:"files" dc:"上传URL列表"`
}

type BatchUploadResult struct {
	FileName  string `json:"fileName" dc:"文件名"`
	UploadUrl string `json:"uploadUrl" dc:"上传预签名URL"`
	FileUrl   string `json:"fileUrl" dc:"文件访问URL"`
	Success   bool   `json:"success" dc:"是否成功"`
	Message   string `json:"message" dc:"错误消息"`
}

// 文件存储统计请求
type FileStatsReq struct {
	g.Meta    `path:"/stats" tags:"File" method:"get" summary:"获取文件存储统计"`
	StartDate string `json:"startDate" v:"date" dc:"开始日期"`
	EndDate   string `json:"endDate" v:"date" dc:"结束日期"`
	FileType  string `json:"fileType" v:"in:avatar,foodimage,document" dc:"文件类型筛选"`
}

type FileStatsRes struct {
	TotalFiles    int64   `json:"totalFiles" dc:"总文件数"`
	TotalSize     int64   `json:"totalSize" dc:"总存储大小（字节）"`
	AvgFileSize   float64 `json:"avgFileSize" dc:"平均文件大小"`
	TypeStats     []FileTypeStat `json:"typeStats" dc:"按类型统计"`
	DailyStats    []FileDailyStat `json:"dailyStats" dc:"按日期统计"`
}

type FileTypeStat struct {
	FileType  string  `json:"fileType" dc:"文件类型"`
	Count     int64   `json:"count" dc:"文件数量"`
	TotalSize int64   `json:"totalSize" dc:"总大小"`
	AvgSize   float64 `json:"avgSize" dc:"平均大小"`
}

type FileDailyStat struct {
	Date      string `json:"date" dc:"日期"`
	Count     int64  `json:"count" dc:"文件数量"`
	TotalSize int64  `json:"totalSize" dc:"总大小"`
}
