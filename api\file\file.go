package file

import (
	"context"

	"shikeyinxiang-goframe/api/file/v1"
)

// IFileV1 文件管理接口定义
type IFileV1 interface {
	// FileUpload 获取文件上传预签名URL
	FileUpload(ctx context.Context, req *v1.FileUploadReq) (res *v1.FileUploadRes, err error)
	
	// FileDownload 获取文件下载预签名URL
	FileDownload(ctx context.Context, req *v1.FileDownloadReq) (res *v1.FileDownloadRes, err error)
	
	// FileDelete 删除文件
	FileDelete(ctx context.Context, req *v1.FileDeleteReq) (res *v1.FileDeleteRes, err error)
	
	// FileList 获取文件列表
	FileList(ctx context.Context, req *v1.FileListReq) (res *v1.FileListRes, err error)
	
	// AvatarUpload 获取头像上传URL
	AvatarUpload(ctx context.Context, req *v1.AvatarUploadReq) (res *v1.AvatarUploadRes, err error)
	
	// AvatarDownload 获取头像下载URL
	AvatarDownload(ctx context.Context, req *v1.AvatarDownloadReq) (res *v1.AvatarDownloadRes, err error)
	
	// FoodImageUpload 获取食物图片上传URL
	FoodImageUpload(ctx context.Context, req *v1.FoodImageUploadReq) (res *v1.FoodImageUploadRes, err error)
	
	// ImageProcess 图片处理
	ImageProcess(ctx context.Context, req *v1.ImageProcessReq) (res *v1.ImageProcessRes, err error)
	
	// FileInfo 获取文件信息
	FileInfo(ctx context.Context, req *v1.FileInfoReq) (res *v1.FileInfoRes, err error)
	
	// BatchUpload 批量获取文件上传URL
	BatchUpload(ctx context.Context, req *v1.BatchUploadReq) (res *v1.BatchUploadRes, err error)
	
	// FileStats 获取文件存储统计（管理员功能）
	FileStats(ctx context.Context, req *v1.FileStatsReq) (res *v1.FileStatsRes, err error)
}
