package user

import (
	"context"

	"shikeyinxiang-goframe/api/user/v1"
)

// IUserV1 用户管理接口定义
type IUserV1 interface {
	// UserCreate 创建用户（管理员功能）
	UserCreate(ctx context.Context, req *v1.UserCreateReq) (res *v1.UserCreateRes, err error)
	
	// UserUpdate 更新用户信息（管理员功能）
	UserUpdate(ctx context.Context, req *v1.UserUpdateReq) (res *v1.UserUpdateRes, err error)
	
	// UserQuery 获取用户详情（管理员功能）
	UserQuery(ctx context.Context, req *v1.UserQueryReq) (res *v1.UserQueryRes, err error)
	
	// UserList 获取用户列表（管理员功能）
	UserList(ctx context.Context, req *v1.UserListReq) (res *v1.UserListRes, err error)
	
	// UserStatusUpdate 更新用户状态（管理员功能）
	UserStatusUpdate(ctx context.Context, req *v1.UserStatusUpdateReq) (res *v1.UserStatusUpdateRes, err error)
	
	// AvatarUpload 上传头像
	AvatarUpload(ctx context.Context, req *v1.AvatarUploadReq) (res *v1.AvatarUploadRes, err error)
	
	// CurrentUserInfo 获取当前用户信息
	CurrentUserInfo(ctx context.Context, req *v1.CurrentUserInfoReq) (res *v1.CurrentUserInfoRes, err error)
	
	// CurrentUserUpdate 更新当前用户信息
	CurrentUserUpdate(ctx context.Context, req *v1.CurrentUserUpdateReq) (res *v1.CurrentUserUpdateRes, err error)
}
